/**
 * Centralized Supabase Client
 *
 * This module provides a centralized way to access the Supabase client.
 * It ensures that only one instance of the client is created and reused.
 * It also provides utilities for working with Supabase.
 */

import { createClient, SupabaseClient, PostgrestError } from '@supabase/supabase-js';
import { logger } from './logger';

/**
 * Custom error class for Supabase errors
 */
export class SupabaseError extends Error {
  public readonly code: string;
  public readonly details?: any;
  public readonly originalError: PostgrestError | Error;

  constructor(
    message: string,
    originalError: PostgrestError | Error,
    code: string = 'SUPABASE_ERROR',
    details?: any,
  ) {
    super(message);
    this.name = 'SupabaseError';
    this.code = code;
    this.details = details;
    this.originalError = originalError;
  }
}

/**
 * Check if an error is a PostgrestError
 *
 * @param error Error to check
 * @returns Whether the error is a PostgrestError
 */
export function isPostgrestError(error: any): error is PostgrestError {
  return (
    error &&
    typeof error === 'object' &&
    'code' in error &&
    'message' in error &&
    'details' in error
  );
}

// Cache for the Supabase client instance
let supabaseClient: SupabaseClient | null = null;
let supabaseAdminClient: SupabaseClient | null = null;

/**
 * Get the Supabase client instance
 *
 * @param options - Options for creating the client
 * @param options.supabaseUrl - Supabase URL
 * @param options.supabaseKey - Supabase API key
 * @returns Supabase client instance
 */
export function getSupabaseClient(
  options: { supabaseUrl?: string; supabaseKey?: string } = {},
): SupabaseClient {
  // Return cached client if available
  if (supabaseClient) {
    return supabaseClient;
  }

  // Get configuration from options or environment variables
  const supabaseUrl = options.supabaseUrl || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = options.supabaseKey || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Validate configuration
  if (!supabaseUrl || !supabaseKey) {
    const error = new Error(
      'Supabase URL and key are required. Set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.',
    );
    logger.error('Failed to create Supabase client', { error });
    throw error;
  }

  try {
    // Create and cache the client
    supabaseClient = createClient(supabaseUrl, supabaseKey);
    logger.info('Supabase client created');
    return supabaseClient;
  } catch (error) {
    logger.error('Failed to create Supabase client', { error });
    throw error;
  }
}

/**
 * Get the Supabase admin client instance
 *
 * @param options - Options for creating the client
 * @param options.supabaseUrl - Supabase URL
 * @param options.supabaseServiceKey - Supabase service role key
 * @returns Supabase admin client instance
 */
export function getSupabaseAdminClient(
  options: { supabaseUrl?: string; supabaseServiceKey?: string } = {},
): SupabaseClient {
  // Return cached client if available
  if (supabaseAdminClient) {
    return supabaseAdminClient;
  }

  // Get configuration from options or environment variables
  const supabaseUrl = options.supabaseUrl || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = options.supabaseServiceKey || process.env.SUPABASE_SERVICE_ROLE_KEY;

  // Validate configuration
  if (!supabaseUrl || !supabaseServiceKey) {
    const error = new Error(
      'Supabase URL and service role key are required. Set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.',
    );
    logger.error('Failed to create Supabase admin client', { error });
    throw error;
  }

  try {
    // Create and cache the client
    supabaseAdminClient = createClient(supabaseUrl, supabaseServiceKey);
    logger.info('Supabase admin client created');
    return supabaseAdminClient;
  } catch (error) {
    logger.error('Failed to create Supabase admin client', { error });
    throw error;
  }
}

/**
 * Reset the Supabase client instances
 *
 * This is useful for testing or when the configuration changes.
 */
export function resetSupabaseClients(): void {
  supabaseClient = null;
  supabaseAdminClient = null;
  logger.info('Supabase clients reset');
}

/**
 * Decorator for retrying Supabase operations
 *
 * @param maxRetries - Maximum number of retries
 * @param delayMs - Delay between retries in milliseconds
 * @returns Decorator function
 */
export function withSupabaseRetry(maxRetries = 3, delayMs = 1000) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let lastError: any = null;

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error: any) {
          lastError = error;

          // Check if this is the last attempt
          if (attempt === maxRetries - 1) {
            logger.error(`Failed after ${maxRetries} attempts`, { error });
            throw error;
          }

          // Log the error and retry
          logger.warn(`Attempt ${attempt + 1} failed, retrying...`, { error });

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }

      // This should never be reached, but TypeScript requires it
      throw lastError;
    };

    return descriptor;
  };
}

// Export lazy-loaded instances for convenience
export const supabase = (() => {
  let instance: SupabaseClient | null = null;
  return () => {
    if (!instance) {
      instance = getSupabaseClient();
    }
    return instance;
  };
})();

export const supabaseAdmin = (() => {
  let instance: SupabaseClient | null = null;
  return () => {
    if (!instance) {
      instance = getSupabaseAdminClient();
    }
    return instance;
  };
})();

/**
 * Helper function to handle Supabase errors
 *
 * @param error Error from Supabase
 * @param operation Description of the operation that failed
 * @throws SupabaseError
 */
export function handleSupabaseError(error: unknown, operation: string): never {
  if (isPostgrestError(error)) {
    throw new SupabaseError(
      `Supabase error during ${operation}: ${error.message}`,
      error,
      error.code,
      error.details,
    );
  } else if (error instanceof Error) {
    throw new SupabaseError(`Error during ${operation}: ${error.message}`, error);
  } else {
    throw new SupabaseError(`Unknown error during ${operation}`, new Error('Unknown error'));
  }
}

/**
 * Execute a Supabase query with error handling
 *
 * @param operation Description of the operation
 * @param query Function that returns a Supabase query
 * @returns Result of the query
 * @throws SupabaseError
 */
export async function executeSupabaseQuery<T>(
  operation: string,
  query: () => Promise<{ data: T | null; error: PostgrestError | null }>,
): Promise<T> {
  try {
    const { data, error } = await query();

    if (error) {
      handleSupabaseError(error, operation);
    }

    if (data === null) {
      throw new SupabaseError(
        `No data returned during ${operation}`,
        new Error('No data returned'),
      );
    }

    return data;
  } catch (error) {
    if (error instanceof SupabaseError) {
      throw error;
    }

    handleSupabaseError(error, operation);
  }
}

export default {
  getSupabaseClient,
  getSupabaseAdminClient,
  resetSupabaseClients,
  withSupabaseRetry,
  isPostgrestError,
  handleSupabaseError,
  executeSupabaseQuery,
  SupabaseError,
  supabase: () => supabase(),
  supabaseAdmin: () => supabaseAdmin(),
};
