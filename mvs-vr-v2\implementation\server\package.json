{"name": "mvs-vr-server", "version": "1.0.0", "type": "module", "scripts": {"start": "node dist/server.js", "dev": "tsx watch server.ts", "build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit false", "prebuild": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:visual-editors": "vitest run tests/unit/visual-editors", "test:report": "vitest run --coverage && node tests/generate-test-report.js", "test:stability": "node scripts/test-stability-monitor.js", "test:stable": "vitest run tests/unit/api-key-middleware.test.ts tests/unit/simple-rate-limit.test.ts tests/unit/simple-vitest.test.ts tests/unit/visual-editors/visual-editors-api.test.ts", "test:advanced": "vitest run tests/realtime tests/security tests/monitoring tests/ml", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:load": "k6 run tests/load/advanced-load-testing-framework.js", "test:load:smoke": "k6 run tests/load/advanced-load-testing-framework.js --env TEST_TYPE=smoke", "test:load:stress": "k6 run tests/load/advanced-load-testing-framework.js --env TEST_TYPE=stress", "test:smoke": "vitest run tests/smoke", "test:comprehensive": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:comprehensive-integration": "vitest run tests/integration/comprehensive-integration-test-suite.ts", "test:ci": "npm run test:unit -- --reporter=json --outputFile=test-results/unit-tests.json && npm run test:integration -- --reporter=json --outputFile=test-results/integration-tests.json", "test:analyze": "node tests/monitoring/analyze-test-results.js", "test:performance-report": "node tests/monitoring/generate-performance-report.js", "test:monitor": "node tests/monitoring/test-monitoring-service.js", "test:local": "node scripts/switch-test-env.js local && npm test", "test:staging": "node scripts/run-staging-tests.js", "test:staging:validate": "node scripts/validate-staging-connection.js", "test:staging:watch": "node scripts/run-staging-tests.js --watch", "test:staging:coverage": "node scripts/run-staging-tests.js --coverage", "test:production": "node scripts/switch-test-env.js production && npm test", "test:env": "node scripts/switch-test-env.js", "test:performance": "vitest run tests/unit/performance-testing.test.ts", "test:security": "vitest run tests/unit/security-testing.test.ts", "test:all": "npm run test:unit && npm run test:integration && npm run test:performance && npm run test:security", "start:advanced": "node scripts/init-advanced-features.js", "start:services": "docker-compose -f docker-compose.yml -f docker-compose.advanced.yml up -d", "stop:services": "docker-compose -f docker-compose.yml -f docker-compose.advanced.yml down", "monitor:logs": "docker-compose logs -f", "monitor:metrics": "echo 'Open http://localhost:9090'", "monitor:dashboard": "echo 'Open http://localhost:3001'"}, "dependencies": {"@aws-sdk/client-s3": "^3.478.0", "@grpc/grpc-js": "^1.9.14", "@grpc/proto-loader": "^0.7.10", "@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-jaeger": "^1.30.1", "@opentelemetry/exporter-prometheus": "^0.45.1", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-metrics": "^1.30.1", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@supabase/supabase-js": "^2.38.0", "@tensorflow/tfjs-node": "^4.22.0", "@testing-library/dom": "^9.3.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "expect": "^29.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.2.1", "helmet": "^7.2.0", "http-proxy-middleware": "^2.0.6", "ioredis": "^5.3.2", "iron-session": "^8.0.4", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "ml-matrix": "^6.12.1", "multer": "^1.4.5-lts.1", "node-jose": "^2.2.0", "pino": "^8.16.2", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.0", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "swagger-parser": "^10.0.3", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "vitest": "^3.1.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.18.2", "yamljs": "^0.3.0", "zod": "^3.22.4", "zod-validation-error": "^3.4.1"}, "devDependencies": {"@google-cloud/storage": "^7.7.0", "@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.6.3", "@types/compression": "^1.7.2", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.7", "@types/node": "^20.2.5", "@types/sinon": "^17.0.2", "@types/supertest": "^2.0.16", "@types/testing-library__jest-dom": "^5.14.6", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^3.1.4", "@vue/test-utils": "^2.4.6", "chai": "^4.3.10", "eslint": "^8.41.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-testing-library": "^5.11.0", "eslint-plugin-vitest": "^0.2.2", "fast-check": "^3.15.0", "happy-dom": "^17.5.6", "jsdom": "^26.1.0", "papaparse": "^5.4.1", "pixelmatch": "^5.3.0", "puppeteer": "^24.9.0", "sinon": "^17.0.1", "supertest": "^6.3.3", "typescript": "^5.0.4", "vite": "^6.3.5", "vue": "^3.5.14"}}