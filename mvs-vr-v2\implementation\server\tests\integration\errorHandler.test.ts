/**
 * <PERSON><PERSON><PERSON> Handler Tests
 *
 * This module contains tests for the Error Handler.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { <PERSON>rror<PERSON>andler } from '../../services/integration/errorHandler.ts';
import { Logger, LogLevel } from '../../services/integration/logger.ts';

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;
  let logger: ReturnType<typeof Logger.getInstance>;

  beforeEach(() => {
    // Create a new logger using the correct singleton pattern
    logger = Logger.getInstance({ level: LogLevel.DEBUG });

    // Mock the logger methods
    logger.error = vi.fn();
    logger.debug = vi.fn();
    logger.info = vi.fn();

    // Create a new error handler
    errorHandler = new ErrorHandler(logger);
  });

  describe('handleError', () => {
    it('should log and report an error', () => {
      // Create an error
      const error = new Error('Test error');
      const context = 'Test context';

      // Handle the error
      errorHandler.handleError(error, context);

      // Verify that the error was logged and reported
      expect(logger.error).toHaveBeenCalledWith(`Error in ${context}: ${error.message}`);
      expect(logger.debug).toHaveBeenCalledWith(`Stack trace: ${error.stack}`);
      expect(logger.info).toHaveBeenCalledWith(
        `Would report error in ${context} to monitoring service`,
      );
    });

    it('should handle non-Error objects', () => {
      // Create a non-Error object
      const error = 'Test error';
      const context = 'Test context';

      // Handle the error
      errorHandler.handleError(error, context);

      // Verify that the error was logged and reported
      expect(logger.error).toHaveBeenCalledWith(`Error in ${context}: ${error}`);
      expect(logger.debug).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        `Would report error in ${context} to monitoring service`,
      );
    });

    it('should use a default context if none is provided', () => {
      // Create an error
      const error = new Error('Test error');

      // Handle the error
      errorHandler.handleError(error);

      // Verify that the error was logged and reported with the default context
      expect(logger.error).toHaveBeenCalledWith('Error in Unknown context: Test error');
      expect(logger.debug).toHaveBeenCalledWith(`Stack trace: ${error.stack}`);
      expect(logger.info).toHaveBeenCalledWith(
        'Would report error in Unknown context to monitoring service',
      );
    });
  });

  describe('createErrorResponse', () => {
    it('should create a standardized error response for an Error object', () => {
      // Create an error
      const error = new Error('Test error');
      const context = 'Test context';

      // Create an error response
      const response = errorHandler.createErrorResponse(error, context);

      // Verify that the response has the correct structure
      expect(response).toEqual({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Test error',
          context: 'Test context',
        },
      });
    });

    it('should create a standardized error response for a non-Error object', () => {
      // Create a non-Error object
      const error = 'Test error';
      const context = 'Test context';

      // Create an error response
      const response = errorHandler.createErrorResponse(error, context);

      // Verify that the response has the correct structure
      expect(response).toEqual({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Test error',
          context: 'Test context',
        },
      });
    });
  });
});
