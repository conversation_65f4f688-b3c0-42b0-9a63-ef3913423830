#!/bin/bash

# MVS-VR Docker Image Export Script for DigitalOcean Deployment
# This script builds, tags, and exports Docker images for easy upload to DigitalOcean

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="mvs-vr-v2"
EXPORT_DIR="$PROJECT_DIR/docker-exports"
REGISTRY_PREFIX="registry.digitalocean.com/mvs-vr"
VERSION_TAG="${VERSION_TAG:-$(date +%Y%m%d-%H%M%S)}"
LATEST_TAG="latest"

# Service definitions
declare -A SERVICES=(
    ["api-gateway"]="Dockerfile.gateway"
    ["asset-service"]="Dockerfile.asset-service"
    ["scene-service"]="Dockerfile.scene-service"
    ["blueprint-service"]="Dockerfile.blueprint-service"
    ["llm-service"]="Dockerfile.llm-service"
    ["auth-service"]="Dockerfile.auth-service"
    ["analytics-service"]="Dockerfile.analytics-service"
    ["monitoring-service"]="Dockerfile.monitoring-service"
    ["directus"]="Dockerfile.directus"
)

# Options
BUILD_IMAGES=true
EXPORT_IMAGES=true
PUSH_TO_REGISTRY=false
COMPRESS_EXPORTS=true
CLEANUP_AFTER_EXPORT=false
VERBOSE=false
DRY_RUN=false

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
MVS-VR Docker Image Export Script

Usage: $0 [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -v, --verbose           Enable verbose output
    -n, --dry-run          Show what would be done without executing
    -s, --skip-build       Skip building images (use existing)
    -e, --skip-export      Skip exporting images to tar files
    -p, --push-registry    Push images to DigitalOcean Container Registry
    -c, --compress         Compress exported tar files (default: true)
    -C, --cleanup          Remove local images after export
    --version VERSION      Set custom version tag (default: timestamp)
    --registry PREFIX      Set custom registry prefix (default: $REGISTRY_PREFIX)

EXAMPLES:
    $0                              # Build and export all images
    $0 --push-registry              # Build, export, and push to DO registry
    $0 --skip-build --export        # Export existing images only
    $0 --version v1.0.0             # Use custom version tag
    $0 --dry-run                    # Preview what would be done

OUTPUTS:
    - Docker images tagged for deployment
    - Exported tar files in $EXPORT_DIR
    - Deployment manifest with image information
    - Upload instructions and scripts

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    # Check available disk space (need at least 10GB)
    AVAILABLE_SPACE=$(df "$PROJECT_DIR" | awk 'NR==2 {print $4}')
    if [[ $AVAILABLE_SPACE -lt 10485760 ]]; then # 10GB in KB
        log_warning "Low disk space detected. You may need at least 10GB free for image exports."
    fi

    # Create export directory
    mkdir -p "$EXPORT_DIR"

    log_success "Prerequisites check passed"
}

build_images() {
    if [[ "$BUILD_IMAGES" != "true" ]]; then
        log_info "Skipping image build (--skip-build specified)"
        return
    fi

    log_info "Building Docker images..."
    cd "$PROJECT_DIR"

    local build_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        build_args="--progress=plain"
    fi

    for service in "${!SERVICES[@]}"; do
        local dockerfile="${SERVICES[$service]}"
        local image_name="$PROJECT_NAME-$service"

        log_info "Building $service using $dockerfile..."

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "Would run: docker build $build_args -f implementation/server/$dockerfile -t $image_name:$VERSION_TAG -t $image_name:$LATEST_TAG implementation/server"
        else
            docker build $build_args \
                -f "implementation/server/$dockerfile" \
                -t "$image_name:$VERSION_TAG" \
                -t "$image_name:$LATEST_TAG" \
                implementation/server
        fi

        log_success "Built $service image"
    done

    log_success "All images built successfully"
}

tag_images_for_registry() {
    log_info "Tagging images for registry deployment..."

    for service in "${!SERVICES[@]}"; do
        local image_name="$PROJECT_NAME-$service"
        local registry_image="$REGISTRY_PREFIX/$service"

        log_info "Tagging $service for registry..."

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "Would run: docker tag $image_name:$VERSION_TAG $registry_image:$VERSION_TAG"
            echo "Would run: docker tag $image_name:$LATEST_TAG $registry_image:$LATEST_TAG"
        else
            docker tag "$image_name:$VERSION_TAG" "$registry_image:$VERSION_TAG"
            docker tag "$image_name:$LATEST_TAG" "$registry_image:$LATEST_TAG"
        fi
    done

    log_success "Images tagged for registry"
}

export_images() {
    if [[ "$EXPORT_IMAGES" != "true" ]]; then
        log_info "Skipping image export (--skip-export specified)"
        return
    fi

    log_info "Exporting Docker images to tar files..."

    local export_manifest="$EXPORT_DIR/export-manifest.json"
    echo "{" > "$export_manifest"
    echo "  \"export_date\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"," >> "$export_manifest"
    echo "  \"version\": \"$VERSION_TAG\"," >> "$export_manifest"
    echo "  \"images\": {" >> "$export_manifest"

    local first=true
    for service in "${!SERVICES[@]}"; do
        local image_name="$PROJECT_NAME-$service"
        local export_file="$EXPORT_DIR/$service-$VERSION_TAG.tar"

        if [[ "$first" != "true" ]]; then
            echo "," >> "$export_manifest"
        fi
        first=false

        log_info "Exporting $service..."

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "Would run: docker save -o $export_file $image_name:$VERSION_TAG"
        else
            docker save -o "$export_file" "$image_name:$VERSION_TAG"

            # Get image size and info
            local image_size=$(stat -f%z "$export_file" 2>/dev/null || stat -c%s "$export_file" 2>/dev/null || echo "unknown")
            local image_id=$(docker images --format "{{.ID}}" "$image_name:$VERSION_TAG")

            echo "    \"$service\": {" >> "$export_manifest"
            echo "      \"file\": \"$service-$VERSION_TAG.tar\"," >> "$export_manifest"
            echo "      \"size\": $image_size," >> "$export_manifest"
            echo "      \"image_id\": \"$image_id\"," >> "$export_manifest"
            echo "      \"tags\": [\"$VERSION_TAG\", \"$LATEST_TAG\"]" >> "$export_manifest"
            echo "    }" >> "$export_manifest"

            # Compress if requested
            if [[ "$COMPRESS_EXPORTS" == "true" ]]; then
                log_info "Compressing $service export..."
                gzip "$export_file"
                export_file="$export_file.gz"
            fi
        fi

        log_success "Exported $service to $(basename "$export_file")"
    done

    echo "" >> "$export_manifest"
    echo "  }" >> "$export_manifest"
    echo "}" >> "$export_manifest"

    log_success "All images exported to $EXPORT_DIR"
}

push_to_registry() {
    if [[ "$PUSH_TO_REGISTRY" != "true" ]]; then
        log_info "Skipping registry push (--push-registry not specified)"
        return
    fi

    log_info "Pushing images to DigitalOcean Container Registry..."

    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        log_warning "doctl CLI not found. Please install it to push to DigitalOcean registry."
        log_info "Install: https://docs.digitalocean.com/reference/doctl/how-to/install/"
        return
    fi

    # Login to registry
    log_info "Logging into DigitalOcean Container Registry..."
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "Would run: doctl registry login"
    else
        doctl registry login
    fi

    for service in "${!SERVICES[@]}"; do
        local registry_image="$REGISTRY_PREFIX/$service"

        log_info "Pushing $service to registry..."

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "Would run: docker push $registry_image:$VERSION_TAG"
            echo "Would run: docker push $registry_image:$LATEST_TAG"
        else
            docker push "$registry_image:$VERSION_TAG"
            docker push "$registry_image:$LATEST_TAG"
        fi

        log_success "Pushed $service to registry"
    done

    log_success "All images pushed to registry"
}

create_deployment_scripts() {
    log_info "Creating deployment scripts..."

    # Create image loader script
    cat > "$EXPORT_DIR/load-images.sh" << 'EOF'
#!/bin/bash
# Load exported Docker images on target server

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

if [[ ! -f "$MANIFEST_FILE" ]]; then
    echo "Error: export-manifest.json not found"
    exit 1
fi

echo "Loading Docker images from exports..."

# Extract version from manifest
VERSION=$(grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4)
echo "Loading images for version: $VERSION"

# Load each image
for tar_file in "$SCRIPT_DIR"/*.tar*; do
    if [[ -f "$tar_file" ]]; then
        echo "Loading $(basename "$tar_file")..."

        # Handle compressed files
        if [[ "$tar_file" == *.gz ]]; then
            gunzip -c "$tar_file" | docker load
        else
            docker load -i "$tar_file"
        fi

        echo "Loaded $(basename "$tar_file")"
    fi
done

echo "All images loaded successfully!"
echo "Run 'docker images' to see loaded images"
EOF

    chmod +x "$EXPORT_DIR/load-images.sh"

    # Create deployment script for DigitalOcean
    cat > "$EXPORT_DIR/deploy-to-digitalocean.sh" << 'EOF'
#!/bin/bash
# Deploy MVS-VR to DigitalOcean using exported images

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

if [[ ! -f "$MANIFEST_FILE" ]]; then
    echo "Error: export-manifest.json not found"
    exit 1
fi

echo "Deploying MVS-VR to DigitalOcean..."

# Load images first
echo "Loading Docker images..."
"$SCRIPT_DIR/load-images.sh"

# Extract version
VERSION=$(grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4)

# Create docker-compose override for exported images
cat > docker-compose.exported.yml << COMPOSE_EOF
version: '3.8'

services:
  api-gateway:
    image: mvs-vr-v2-api-gateway:$VERSION
    build: null

  asset-service:
    image: mvs-vr-v2-asset-service:$VERSION
    build: null

  scene-service:
    image: mvs-vr-v2-scene-service:$VERSION
    build: null

  blueprint-service:
    image: mvs-vr-v2-blueprint-service:$VERSION
    build: null

  llm-service:
    image: mvs-vr-v2-llm-service:$VERSION
    build: null

  auth-service:
    image: mvs-vr-v2-auth-service:$VERSION
    build: null

  analytics-service:
    image: mvs-vr-v2-analytics-service:$VERSION
    build: null

  monitoring-service:
    image: mvs-vr-v2-monitoring-service:$VERSION
    build: null

  directus:
    image: mvs-vr-v2-directus:$VERSION
    build: null
COMPOSE_EOF

echo "Starting services with exported images..."
docker-compose -f docker-compose.yml -f docker-compose.exported.yml up -d

echo "Deployment completed!"
echo "Check service status with: docker-compose ps"
EOF

    chmod +x "$EXPORT_DIR/deploy-to-digitalocean.sh"

    log_success "Deployment scripts created"
}

create_upload_instructions() {
    log_info "Creating upload instructions..."

    cat > "$EXPORT_DIR/UPLOAD_INSTRUCTIONS.md" << 'EOF'
# Docker Image Upload Instructions for DigitalOcean

## Overview
This directory contains exported Docker images and deployment scripts for MVS-VR v2.

## Files Included
- `*.tar` or `*.tar.gz` - Exported Docker images
- `export-manifest.json` - Image metadata and information
- `load-images.sh` - Script to load images on target server
- `deploy-to-digitalocean.sh` - Complete deployment script
- `UPLOAD_INSTRUCTIONS.md` - This file

## Method 1: Upload via SCP/RSYNC (Recommended)

### Step 1: Upload files to DigitalOcean Droplet
```bash
# Using SCP
scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/

# Using RSYNC (more efficient for large files)
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/
```

### Step 2: Connect to server and deploy
```bash
ssh root@YOUR_SERVER_IP
cd /opt/mvs-vr/docker-exports
./deploy-to-digitalocean.sh
```

## Method 2: Using DigitalOcean Container Registry

### Step 1: Push images to registry (from local machine)
```bash
# Run the export script with registry push
./scripts/export-docker-images.sh --push-registry

# Or manually push after export
doctl registry login
docker push registry.digitalocean.com/mvs-vr/api-gateway:VERSION_TAG
# ... repeat for all services
```

### Step 2: Deploy from registry (on server)
```bash
# Pull and deploy from registry
docker-compose -f docker-compose.registry.yml up -d
```

## Method 3: Using DigitalOcean Spaces (Object Storage)

### Step 1: Upload to Spaces
```bash
# Install s3cmd or use DigitalOcean web interface
s3cmd put docker-exports/*.tar.gz s3://your-space-name/mvs-vr-images/

# Or use doctl
doctl compute cdn create --origin your-space-name.nyc3.digitaloceanspaces.com
```

### Step 2: Download on server
```bash
# Download from Spaces
wget https://your-space-name.nyc3.digitaloceanspaces.com/mvs-vr-images/api-gateway-VERSION.tar.gz
# ... repeat for all images

# Load images
./load-images.sh
```

## Verification

After deployment, verify services are running:
```bash
# Check service status
docker-compose ps

# Check service health
curl http://localhost:3000/health
curl http://localhost:8055/server/health

# View logs
docker-compose logs -f
```

## Troubleshooting

### Large File Upload Issues
- Use `rsync` with `--partial` flag for resumable uploads
- Consider splitting large files: `split -b 1G large-image.tar.gz`

### Network Issues
- Use compression: `tar czf` instead of `tar cf`
- Upload during off-peak hours
- Consider using DigitalOcean's private networking

### Storage Space
- Clean up old images: `docker system prune -a`
- Use multi-stage builds to reduce image sizes
- Monitor disk usage: `df -h`

## Security Notes
- Use SSH keys instead of passwords
- Ensure firewall rules are properly configured
- Keep exported images in secure locations
- Rotate credentials regularly
EOF

    log_success "Upload instructions created"
}

cleanup_local_images() {
    if [[ "$CLEANUP_AFTER_EXPORT" != "true" ]]; then
        log_info "Skipping local image cleanup (--cleanup not specified)"
        return
    fi

    log_warning "Cleaning up local images..."

    for service in "${!SERVICES[@]}"; do
        local image_name="$PROJECT_NAME-$service"
        local registry_image="$REGISTRY_PREFIX/$service"

        if [[ "$DRY_RUN" == "true" ]]; then
            echo "Would remove: $image_name:$VERSION_TAG $image_name:$LATEST_TAG"
            echo "Would remove: $registry_image:$VERSION_TAG $registry_image:$LATEST_TAG"
        else
            docker rmi "$image_name:$VERSION_TAG" "$image_name:$LATEST_TAG" || true
            docker rmi "$registry_image:$VERSION_TAG" "$registry_image:$LATEST_TAG" || true
        fi
    done

    log_success "Local images cleaned up"
}

show_summary() {
    log_success "Docker image export completed!"
    echo ""
    echo "=== Export Summary ==="
    echo "Version: $VERSION_TAG"
    echo "Export Directory: $EXPORT_DIR"
    echo "Registry Prefix: $REGISTRY_PREFIX"
    echo ""

    if [[ -d "$EXPORT_DIR" ]]; then
        echo "=== Exported Files ==="
        ls -lh "$EXPORT_DIR"
        echo ""

        echo "=== Total Export Size ==="
        du -sh "$EXPORT_DIR"
        echo ""
    fi

    echo "=== Next Steps ==="
    echo "1. Review upload instructions: $EXPORT_DIR/UPLOAD_INSTRUCTIONS.md"
    echo "2. Upload files to DigitalOcean server"
    echo "3. Run deployment script on server"
    echo "4. Verify service health and functionality"
    echo ""
    echo "=== Quick Upload Command ==="
    echo "rsync -avz --progress $EXPORT_DIR/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/"
}

# Main execution function
main() {
    log_info "Starting Docker image export process..."

    check_prerequisites
    build_images
    tag_images_for_registry
    export_images
    push_to_registry
    create_deployment_scripts
    create_upload_instructions
    cleanup_local_images
    show_summary

    log_success "Export process completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -s|--skip-build)
            BUILD_IMAGES=false
            shift
            ;;
        -e|--skip-export)
            EXPORT_IMAGES=false
            shift
            ;;
        -p|--push-registry)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -c|--compress)
            COMPRESS_EXPORTS=true
            shift
            ;;
        -C|--cleanup)
            CLEANUP_AFTER_EXPORT=true
            shift
            ;;
        --version)
            VERSION_TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY_PREFIX="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
