#!/bin/bash

# MVS-VR Automated Deployment Script with Image Management
# This script handles the complete deployment workflow including image export and upload

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_NAME="mvs-vr-v2"

# Default options
DEPLOYMENT_METHOD="export"  # export, registry, hybrid
SERVER_IP=""
SERVER_USER="root"
REMOTE_PATH="/opt/mvs-vr"
VERSION_TAG=""
SKIP_TESTS=false
SKIP_BUILD=false
FORCE_REBUILD=false
BACKUP_BEFORE_DEPLOY=true
VERBOSE=false
DRY_RUN=false
CLEANUP_AFTER=false

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
MVS-VR Automated Deployment Script with Image Management

Usage: $0 [OPTIONS]

REQUIRED:
    --server-ip IP          DigitalOcean server IP address

OPTIONS:
    -h, --help              Show this help message
    -v, --verbose           Enable verbose output
    -n, --dry-run          Show what would be done without executing
    -t, --skip-tests        Skip running tests before deployment
    -b, --skip-build        Skip building Docker images
    -f, --force-rebuild     Force rebuild of all Docker images (no cache)
    --no-backup            Skip backup before deployment
    -c, --cleanup          Clean up local images after deployment
    --version VERSION      Set custom version tag (default: timestamp)
    --method METHOD        Deployment method: export, registry, hybrid (default: export)
    --user USER            SSH user for server connection (default: root)
    --remote-path PATH     Remote deployment path (default: /opt/mvs-vr)

DEPLOYMENT METHODS:
    export                 Build locally, export images, upload and deploy
    registry               Build locally, push to registry, deploy from registry
    hybrid                 Export images AND push to registry for redundancy

EXAMPLES:
    $0 --server-ip *************                           # Basic deployment
    $0 --server-ip ************* --method registry         # Deploy via registry
    $0 --server-ip ************* --version v1.0.0         # Custom version
    $0 --server-ip ************* --dry-run                 # Preview deployment
    $0 --server-ip ************* --skip-tests --cleanup    # Fast deployment

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check required tools
    local required_tools=("docker" "docker-compose" "ssh" "rsync")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed. Please install it first."
            exit 1
        fi
    done

    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    # Check server connectivity
    if [[ -n "$SERVER_IP" ]]; then
        log_info "Testing SSH connectivity to $SERVER_USER@$SERVER_IP..."
        if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_IP" exit 2>/dev/null; then
            log_success "SSH connectivity confirmed"
        else
            log_warning "SSH connectivity test failed. Please ensure SSH keys are configured."
        fi
    fi

    # Check for registry method requirements
    if [[ "$DEPLOYMENT_METHOD" == "registry" || "$DEPLOYMENT_METHOD" == "hybrid" ]]; then
        if ! command -v doctl &> /dev/null; then
            log_warning "doctl CLI not found. Registry deployment may fail."
            log_info "Install: https://docs.digitalocean.com/reference/doctl/how-to/install/"
        fi
    fi

    log_success "Prerequisites check passed"
}

export_images() {
    log_info "Exporting Docker images..."

    local export_args=()
    
    if [[ "$VERBOSE" == "true" ]]; then
        export_args+=("--verbose")
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        export_args+=("--dry-run")
    fi
    
    if [[ "$SKIP_BUILD" == "true" ]]; then
        export_args+=("--skip-build")
    fi
    
    if [[ "$FORCE_REBUILD" == "true" ]]; then
        export_args+=("--force-rebuild")
    fi
    
    if [[ -n "$VERSION_TAG" ]]; then
        export_args+=("--version" "$VERSION_TAG")
    fi
    
    if [[ "$DEPLOYMENT_METHOD" == "registry" || "$DEPLOYMENT_METHOD" == "hybrid" ]]; then
        export_args+=("--push-registry")
    fi
    
    if [[ "$DEPLOYMENT_METHOD" == "registry" ]]; then
        export_args+=("--skip-export")
    fi
    
    if [[ "$CLEANUP_AFTER" == "true" ]]; then
        export_args+=("--cleanup")
    fi

    # Run the export script
    "$SCRIPT_DIR/export-docker-images.sh" "${export_args[@]}"
    
    log_success "Image export completed"
}

upload_to_server() {
    if [[ "$DEPLOYMENT_METHOD" == "registry" ]]; then
        log_info "Skipping upload (using registry deployment)"
        return
    fi

    log_info "Uploading files to server..."

    local export_dir="$PROJECT_DIR/docker-exports"
    local remote_export_dir="$REMOTE_PATH/docker-exports"

    if [[ "$DRY_RUN" == "true" ]]; then
        echo "Would run: rsync -avz --progress $export_dir/ $SERVER_USER@$SERVER_IP:$remote_export_dir/"
        return
    fi

    # Create remote directory
    ssh "$SERVER_USER@$SERVER_IP" "mkdir -p $remote_export_dir"

    # Upload files with progress
    rsync -avz --progress "$export_dir/" "$SERVER_USER@$SERVER_IP:$remote_export_dir/"

    log_success "Files uploaded to server"
}

deploy_on_server() {
    log_info "Deploying services on server..."

    local deploy_commands=""
    
    case "$DEPLOYMENT_METHOD" in
        "export")
            deploy_commands="cd $REMOTE_PATH/docker-exports && ./deploy-to-digitalocean.sh"
            ;;
        "registry")
            deploy_commands="cd $REMOTE_PATH && docker-compose -f docker-compose.registry.yml up -d"
            ;;
        "hybrid")
            deploy_commands="cd $REMOTE_PATH/docker-exports && ./deploy-to-digitalocean.sh"
            ;;
    esac

    if [[ "$DRY_RUN" == "true" ]]; then
        echo "Would run on server: $deploy_commands"
        return
    fi

    # Execute deployment on server
    ssh "$SERVER_USER@$SERVER_IP" "$deploy_commands"

    log_success "Deployment completed on server"
}

run_health_checks() {
    log_info "Running health checks on server..."

    local health_commands="
        echo 'Checking service status...'
        docker-compose ps
        echo ''
        echo 'Testing API health...'
        curl -f http://localhost:3000/health || echo 'API health check failed'
        echo ''
        echo 'Testing Directus health...'
        curl -f http://localhost:8055/server/health || echo 'Directus health check failed'
        echo ''
        echo 'Testing database connectivity...'
        docker-compose exec -T postgres pg_isready -U postgres || echo 'Database check failed'
        echo ''
        echo 'Testing Redis connectivity...'
        docker-compose exec -T redis redis-cli ping || echo 'Redis check failed'
    "

    if [[ "$DRY_RUN" == "true" ]]; then
        echo "Would run health checks on server"
        return
    fi

    ssh "$SERVER_USER@$SERVER_IP" "$health_commands"

    log_success "Health checks completed"
}

backup_server() {
    if [[ "$BACKUP_BEFORE_DEPLOY" != "true" ]]; then
        log_info "Skipping backup (--no-backup specified)"
        return
    fi

    log_info "Creating backup on server..."

    local backup_commands="
        cd $REMOTE_PATH
        BACKUP_DIR=\"backups/\$(date +%Y%m%d_%H%M%S)\"
        mkdir -p \"\$BACKUP_DIR\"
        
        echo 'Backing up database...'
        docker-compose exec -T postgres pg_dump -U postgres mvs_staging > \"\$BACKUP_DIR/database_backup.sql\" || true
        
        echo 'Backing up volumes...'
        docker run --rm -v mvs-vr-v2_postgres_data:/data -v \"\$(pwd)/\$BACKUP_DIR\":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data . || true
        docker run --rm -v mvs-vr-v2_directus_uploads:/data -v \"\$(pwd)/\$BACKUP_DIR\":/backup alpine tar czf /backup/directus_uploads.tar.gz -C /data . || true
        
        echo \"Backup created at \$BACKUP_DIR\"
    "

    if [[ "$DRY_RUN" == "true" ]]; then
        echo "Would create backup on server"
        return
    fi

    ssh "$SERVER_USER@$SERVER_IP" "$backup_commands"

    log_success "Backup completed"
}

run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "Skipping tests (--skip-tests specified)"
        return
    fi

    log_info "Running pre-deployment tests..."

    cd "$PROJECT_DIR/implementation/server"

    # Run stable tests
    if npm run test:stable; then
        log_success "Stable tests passed"
    else
        log_error "Stable tests failed. Deployment aborted."
        exit 1
    fi

    cd "$PROJECT_DIR"
}

show_deployment_summary() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "=== Deployment Summary ==="
    echo "Method: $DEPLOYMENT_METHOD"
    echo "Server: $SERVER_USER@$SERVER_IP"
    echo "Version: ${VERSION_TAG:-$(date +%Y%m%d-%H%M%S)}"
    echo "Timestamp: $(date)"
    echo ""
    echo "=== Service URLs ==="
    echo "API: http://$SERVER_IP:3000/health"
    echo "Admin: http://$SERVER_IP:8055"
    echo "Monitoring: http://$SERVER_IP:3007"
    echo ""
    echo "=== Next Steps ==="
    echo "1. Configure DNS records to point to $SERVER_IP"
    echo "2. Set up SSL certificates"
    echo "3. Run comprehensive tests"
    echo "4. Configure monitoring alerts"
    echo ""
    echo "=== Useful Commands ==="
    echo "SSH to server: ssh $SERVER_USER@$SERVER_IP"
    echo "View logs: ssh $SERVER_USER@$SERVER_IP 'cd $REMOTE_PATH && docker-compose logs -f'"
    echo "Restart services: ssh $SERVER_USER@$SERVER_IP 'cd $REMOTE_PATH && docker-compose restart'"
}

cleanup_on_error() {
    log_error "Deployment failed. Check logs above for details."
    exit 1
}

# Main deployment process
main() {
    log_info "Starting automated MVS-VR deployment..."

    # Set error trap
    trap cleanup_on_error ERR

    # Validate required parameters
    if [[ -z "$SERVER_IP" ]]; then
        log_error "Server IP is required. Use --server-ip option."
        show_help
        exit 1
    fi

    # Run deployment steps
    check_prerequisites
    run_tests
    backup_server
    export_images
    upload_to_server
    deploy_on_server
    run_health_checks
    show_deployment_summary

    log_success "Automated deployment completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -t|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -b|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -f|--force-rebuild)
            FORCE_REBUILD=true
            shift
            ;;
        --no-backup)
            BACKUP_BEFORE_DEPLOY=false
            shift
            ;;
        -c|--cleanup)
            CLEANUP_AFTER=true
            shift
            ;;
        --server-ip)
            SERVER_IP="$2"
            shift 2
            ;;
        --user)
            SERVER_USER="$2"
            shift 2
            ;;
        --remote-path)
            REMOTE_PATH="$2"
            shift 2
            ;;
        --version)
            VERSION_TAG="$2"
            shift 2
            ;;
        --method)
            DEPLOYMENT_METHOD="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate deployment method
case "$DEPLOYMENT_METHOD" in
    "export"|"registry"|"hybrid")
        ;;
    *)
        log_error "Invalid deployment method: $DEPLOYMENT_METHOD"
        log_error "Valid methods: export, registry, hybrid"
        exit 1
        ;;
esac

# Run main function
main "$@"
