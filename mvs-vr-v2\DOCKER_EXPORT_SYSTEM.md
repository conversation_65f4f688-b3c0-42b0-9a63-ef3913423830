# MVS-VR v2 Docker Image Export System

## 🚀 Quick Start

The MVS-VR v2 project now includes a comprehensive Docker image export system for easy deployment to DigitalOcean and other cloud providers.

### One-Command Deployment
```bash
# Complete automated deployment to DigitalOcean
./scripts/deploy-with-images.sh --server-ip YOUR_SERVER_IP
```

### Manual Export and Upload
```bash
# 1. Export images locally
./scripts/export-docker-images.sh --compress

# 2. Upload to server
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/

# 3. Deploy on server
ssh root@YOUR_SERVER_IP 'cd /opt/mvs-vr/docker-exports && ./deploy-to-digitalocean.sh'
```

## 📁 New Files Created

### Core Scripts
- **`scripts/export-docker-images.sh`** - Main export script (Linux/macOS)
- **`scripts/export-docker-images.ps1`** - PowerShell version for Windows
- **`scripts/deploy-with-images.sh`** - Automated deployment workflow
- **`scripts/validate-export-system.sh`** - System validation script

### Docker Compose Files
- **`docker-compose.registry.yml`** - Configuration for DigitalOcean Container Registry deployment

### Documentation
- **`docs/deployment-guides/docker-image-export-guide.md`** - Comprehensive export guide
- **`docs/deployment-guides/DOCKER_EXPORT_README.md`** - Complete system documentation
- **`DOCKER_EXPORT_SYSTEM.md`** - This overview document

### Generated Files (after export)
- **`docker-exports/`** - Directory containing exported images and deployment scripts
- **`docker-exports/load-images.sh`** - Script to load images on target server
- **`docker-exports/deploy-to-digitalocean.sh`** - Deployment script for exported images
- **`docker-exports/export-manifest.json`** - Metadata about exported images
- **`docker-exports/UPLOAD_INSTRUCTIONS.md`** - Detailed upload instructions

## 🎯 Key Features

### Multiple Deployment Methods
1. **Export Method** (Default) - Build locally, export to files, upload and deploy
2. **Registry Method** - Build locally, push to DigitalOcean Container Registry, deploy from registry
3. **Hybrid Method** - Both export files AND push to registry for maximum flexibility

### Cross-Platform Support
- **Linux/macOS**: Bash scripts with full functionality
- **Windows**: PowerShell scripts with equivalent features
- **Docker Desktop**: Works on all platforms with Docker Desktop

### Comprehensive Automation
- **Prerequisites checking** - Validates Docker, tools, and connectivity
- **Health monitoring** - Checks service status after deployment
- **Backup creation** - Automatic backup before deployment
- **Error handling** - Graceful failure handling and cleanup

### Flexible Configuration
- **Custom version tags** - Use semantic versioning or timestamps
- **Compression options** - Reduce file sizes for faster uploads
- **Registry integration** - Seamless DigitalOcean Container Registry support
- **Dry run mode** - Preview actions without execution

## 🛠️ Usage Examples

### Basic Operations

```bash
# Validate system setup
./scripts/validate-export-system.sh

# Export with compression (recommended)
./scripts/export-docker-images.sh --compress

# Export with custom version
./scripts/export-docker-images.sh --version v1.0.0 --compress

# Preview what would be done
./scripts/export-docker-images.sh --dry-run
```

### Registry Operations

```bash
# Push to DigitalOcean Container Registry
./scripts/export-docker-images.sh --push-registry

# Export files AND push to registry
./scripts/export-docker-images.sh --push-registry --compress

# Deploy from registry on server
docker-compose -f docker-compose.registry.yml up -d
```

### Automated Deployment

```bash
# Complete deployment workflow
./scripts/deploy-with-images.sh --server-ip *************

# Deploy using registry method
./scripts/deploy-with-images.sh --server-ip ************* --method registry

# Deploy with custom version
./scripts/deploy-with-images.sh --server-ip ************* --version v1.0.0

# Skip tests and backup for faster deployment
./scripts/deploy-with-images.sh --server-ip ************* --skip-tests --no-backup
```

### Windows PowerShell

```powershell
# Export images on Windows
.\scripts\export-docker-images.ps1 -Compress

# Export with custom version
.\scripts\export-docker-images.ps1 -Version "v1.0.0" -Compress

# Push to registry
.\scripts\export-docker-images.ps1 -PushRegistry

# Preview actions
.\scripts\export-docker-images.ps1 -DryRun
```

## 📊 Export Output

After running the export script, you'll get:

```
docker-exports/
├── api-gateway-20240101-120000.tar.gz      # Service images (compressed)
├── asset-service-20240101-120000.tar.gz
├── scene-service-20240101-120000.tar.gz
├── blueprint-service-20240101-120000.tar.gz
├── llm-service-20240101-120000.tar.gz
├── auth-service-20240101-120000.tar.gz
├── analytics-service-20240101-120000.tar.gz
├── monitoring-service-20240101-120000.tar.gz
├── directus-20240101-120000.tar.gz
├── export-manifest.json                    # Image metadata
├── load-images.sh                          # Image loader script
├── deploy-to-digitalocean.sh               # Deployment script
└── UPLOAD_INSTRUCTIONS.md                  # Upload guide
```

## 🔧 Configuration

### Environment Variables

```bash
# Custom registry settings
export REGISTRY_PREFIX="your-registry.com/mvs-vr"
export VERSION_TAG="v1.0.0"

# DigitalOcean Container Registry
export IMAGE_TAG="latest"
docker-compose -f docker-compose.registry.yml up -d
```

### Script Options

All scripts support comprehensive options:

```bash
# Export script options
--version VERSION      # Custom version tag
--compress            # Compress exported files
--push-registry       # Push to container registry
--skip-build          # Use existing images
--cleanup             # Remove local images after export
--dry-run             # Preview actions
--verbose             # Detailed output

# Deployment script options
--server-ip IP        # Target server IP (required)
--method METHOD       # export, registry, or hybrid
--user USER           # SSH user (default: root)
--skip-tests          # Skip pre-deployment tests
--no-backup           # Skip backup creation
--cleanup             # Clean up after deployment
```

## 🚨 Prerequisites

### Local Development Environment
- Docker and Docker Compose installed
- Sufficient disk space (10GB+ recommended)
- Network access for registry operations (optional)

### DigitalOcean Server
- Ubuntu 22.04 LTS (recommended)
- Docker and Docker Compose installed
- SSH access configured
- Sufficient storage for images and data

### Optional Tools
- **doctl** - DigitalOcean CLI for registry operations
- **rsync** - Efficient file transfer (recommended over scp)
- **ssh** - Remote server access

## 📚 Documentation

### Quick References
- **[DOCKER_EXPORT_README.md](docs/deployment-guides/DOCKER_EXPORT_README.md)** - Complete system documentation
- **[docker-image-export-guide.md](docs/deployment-guides/docker-image-export-guide.md)** - Detailed export guide
- **[digitalocean-step-by-step.md](docs/deployment-guides/digitalocean-step-by-step.md)** - DigitalOcean setup guide

### Script Help
```bash
# Get help for any script
./scripts/export-docker-images.sh --help
./scripts/deploy-with-images.sh --help
./scripts/validate-export-system.sh
```

## 🔍 Validation and Testing

### System Validation
```bash
# Validate entire export system
./scripts/validate-export-system.sh

# Test export without building
./scripts/export-docker-images.sh --dry-run

# Test deployment workflow
./scripts/deploy-with-images.sh --server-ip SERVER_IP --dry-run
```

### Health Checks
```bash
# After deployment, verify services
curl http://SERVER_IP:3000/health
curl http://SERVER_IP:8055/server/health

# Check service status
ssh root@SERVER_IP 'docker-compose ps'
```

## 🚀 Next Steps

1. **Validate Setup**: Run `./scripts/validate-export-system.sh`
2. **Test Export**: Try `./scripts/export-docker-images.sh --dry-run`
3. **Review Documentation**: Read the comprehensive guides in `docs/deployment-guides/`
4. **Configure DigitalOcean**: Set up your server following the step-by-step guide
5. **Deploy**: Use the automated deployment script for your first deployment

## 🤝 Support

For issues or questions:
1. Check the validation script output
2. Review the comprehensive documentation
3. Examine script logs with `--verbose` flag
4. Test with `--dry-run` to preview actions

The export system is designed to be robust and user-friendly, with extensive error checking and helpful output to guide you through any issues.

---

**Ready to deploy?** Start with `./scripts/validate-export-system.sh` to ensure everything is properly configured!
