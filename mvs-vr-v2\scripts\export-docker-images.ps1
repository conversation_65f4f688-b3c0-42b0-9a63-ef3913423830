# MVS-VR Docker Image Export Script for DigitalOcean Deployment (PowerShell)
# This script builds, tags, and exports Docker images for easy upload to DigitalOcean

param(
    [switch]$Help,
    [switch]$Verbose,
    [switch]$DryRun,
    [switch]$SkipBuild,
    [switch]$SkipExport,
    [switch]$PushRegistry,
    [switch]$Compress = $true,
    [switch]$Cleanup,
    [string]$Version = "",
    [string]$Registry = "registry.digitalocean.com/mvs-vr"
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$ProjectName = "mvs-vr-v2"
$ExportDir = Join-Path $ProjectDir "docker-exports"
$VersionTag = if ($Version) { $Version } else { Get-Date -Format "yyyyMMdd-HHmmss" }
$LatestTag = "latest"

# Service definitions
$Services = @{
    "api-gateway" = "Dockerfile.gateway"
    "asset-service" = "Dockerfile.asset-service"
    "scene-service" = "Dockerfile.scene-service"
    "blueprint-service" = "Dockerfile.blueprint-service"
    "llm-service" = "Dockerfile.llm-service"
    "auth-service" = "Dockerfile.auth-service"
    "analytics-service" = "Dockerfile.analytics-service"
    "monitoring-service" = "Dockerfile.monitoring-service"
    "directus" = "Dockerfile.directus"
}

# Color functions
function Write-Info($message) {
    Write-Host "[INFO] $message" -ForegroundColor Blue
}

function Write-Success($message) {
    Write-Host "[SUCCESS] $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "[WARNING] $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "[ERROR] $message" -ForegroundColor Red
}

function Show-Help {
    @"
MVS-VR Docker Image Export Script (PowerShell)

Usage: .\export-docker-images.ps1 [OPTIONS]

OPTIONS:
    -Help               Show this help message
    -Verbose            Enable verbose output
    -DryRun             Show what would be done without executing
    -SkipBuild          Skip building images (use existing)
    -SkipExport         Skip exporting images to tar files
    -PushRegistry       Push images to DigitalOcean Container Registry
    -Compress           Compress exported tar files (default: true)
    -Cleanup            Remove local images after export
    -Version VERSION    Set custom version tag (default: timestamp)
    -Registry PREFIX    Set custom registry prefix (default: $Registry)

EXAMPLES:
    .\export-docker-images.ps1                              # Build and export all images
    .\export-docker-images.ps1 -PushRegistry                # Build, export, and push to DO registry
    .\export-docker-images.ps1 -SkipBuild                   # Export existing images only
    .\export-docker-images.ps1 -Version "v1.0.0"           # Use custom version tag
    .\export-docker-images.ps1 -DryRun                      # Preview what would be done

OUTPUTS:
    - Docker images tagged for deployment
    - Exported tar files in $ExportDir
    - Deployment manifest with image information
    - Upload instructions and scripts
"@
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."

    # Check if Docker is installed and running
    try {
        $dockerVersion = docker --version
        if ($LASTEXITCODE -ne 0) {
            throw "Docker command failed"
        }
        Write-Info "Docker found: $dockerVersion"
    }
    catch {
        Write-Error "Docker is not installed or not running. Please install Docker Desktop first."
        exit 1
    }

    # Check if Docker daemon is running
    try {
        docker info | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Docker daemon not running"
        }
    }
    catch {
        Write-Error "Docker daemon is not running. Please start Docker Desktop first."
        exit 1
    }

    # Check if Docker Compose is available
    try {
        docker-compose --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Docker Compose not found"
        }
    }
    catch {
        Write-Error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    }

    # Check available disk space (need at least 10GB)
    $drive = (Get-Item $ProjectDir).PSDrive
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$($drive.Name):'").FreeSpace
    if ($freeSpace -lt 10GB) {
        Write-Warning "Low disk space detected. You may need at least 10GB free for image exports."
    }

    # Create export directory
    if (-not (Test-Path $ExportDir)) {
        New-Item -ItemType Directory -Path $ExportDir -Force | Out-Null
    }

    Write-Success "Prerequisites check passed"
}

function Build-Images {
    if ($SkipBuild) {
        Write-Info "Skipping image build (-SkipBuild specified)"
        return
    }

    Write-Info "Building Docker images..."
    Set-Location $ProjectDir

    foreach ($service in $Services.Keys) {
        $dockerfile = $Services[$service]
        $imageName = "$ProjectName-$service"
        
        Write-Info "Building $service using $dockerfile..."
        
        if ($DryRun) {
            Write-Host "Would run: docker build -f implementation/server/$dockerfile -t ${imageName}:$VersionTag -t ${imageName}:$LatestTag implementation/server"
        }
        else {
            $buildArgs = @(
                "build",
                "-f", "implementation/server/$dockerfile",
                "-t", "${imageName}:$VersionTag",
                "-t", "${imageName}:$LatestTag",
                "implementation/server"
            )
            
            & docker @buildArgs
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Failed to build $service"
                exit 1
            }
        }
        
        Write-Success "Built $service image"
    }

    Write-Success "All images built successfully"
}

function Add-RegistryTags {
    Write-Info "Tagging images for registry deployment..."

    foreach ($service in $Services.Keys) {
        $imageName = "$ProjectName-$service"
        $registryImage = "$Registry/$service"
        
        Write-Info "Tagging $service for registry..."
        
        if ($DryRun) {
            Write-Host "Would run: docker tag ${imageName}:$VersionTag ${registryImage}:$VersionTag"
            Write-Host "Would run: docker tag ${imageName}:$LatestTag ${registryImage}:$LatestTag"
        }
        else {
            docker tag "${imageName}:$VersionTag" "${registryImage}:$VersionTag"
            docker tag "${imageName}:$LatestTag" "${registryImage}:$LatestTag"
        }
    }

    Write-Success "Images tagged for registry"
}

function Export-Images {
    if ($SkipExport) {
        Write-Info "Skipping image export (-SkipExport specified)"
        return
    }

    Write-Info "Exporting Docker images to tar files..."

    $exportManifest = Join-Path $ExportDir "export-manifest.json"
    $manifest = @{
        export_date = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        version = $VersionTag
        images = @{}
    }

    foreach ($service in $Services.Keys) {
        $imageName = "$ProjectName-$service"
        $exportFile = Join-Path $ExportDir "$service-$VersionTag.tar"
        
        Write-Info "Exporting $service..."
        
        if ($DryRun) {
            Write-Host "Would run: docker save -o $exportFile ${imageName}:$VersionTag"
        }
        else {
            docker save -o $exportFile "${imageName}:$VersionTag"
            
            if ($LASTEXITCODE -eq 0) {
                # Get image size and info
                $imageSize = (Get-Item $exportFile).Length
                $imageId = (docker images --format "{{.ID}}" "${imageName}:$VersionTag").Trim()
                
                $manifest.images[$service] = @{
                    file = "$service-$VersionTag.tar"
                    size = $imageSize
                    image_id = $imageId
                    tags = @($VersionTag, $LatestTag)
                }
                
                # Compress if requested
                if ($Compress) {
                    Write-Info "Compressing $service export..."
                    Compress-Archive -Path $exportFile -DestinationPath "$exportFile.zip" -Force
                    Remove-Item $exportFile
                    $exportFile = "$exportFile.zip"
                    $manifest.images[$service].file = "$service-$VersionTag.tar.zip"
                }
            }
            else {
                Write-Error "Failed to export $service"
                exit 1
            }
        }
        
        Write-Success "Exported $service to $(Split-Path -Leaf $exportFile)"
    }

    # Save manifest
    $manifest | ConvertTo-Json -Depth 3 | Set-Content $exportManifest

    Write-Success "All images exported to $ExportDir"
}

function Push-ToRegistry {
    if (-not $PushRegistry) {
        Write-Info "Skipping registry push (-PushRegistry not specified)"
        return
    }

    Write-Info "Pushing images to DigitalOcean Container Registry..."

    # Check if doctl is installed
    try {
        doctl version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "doctl not found"
        }
    }
    catch {
        Write-Warning "doctl CLI not found. Please install it to push to DigitalOcean registry."
        Write-Info "Install: https://docs.digitalocean.com/reference/doctl/how-to/install/"
        return
    }

    # Login to registry
    Write-Info "Logging into DigitalOcean Container Registry..."
    if ($DryRun) {
        Write-Host "Would run: doctl registry login"
    }
    else {
        doctl registry login
    }

    foreach ($service in $Services.Keys) {
        $registryImage = "$Registry/$service"
        
        Write-Info "Pushing $service to registry..."
        
        if ($DryRun) {
            Write-Host "Would run: docker push ${registryImage}:$VersionTag"
            Write-Host "Would run: docker push ${registryImage}:$LatestTag"
        }
        else {
            docker push "${registryImage}:$VersionTag"
            docker push "${registryImage}:$LatestTag"
        }
        
        Write-Success "Pushed $service to registry"
    }

    Write-Success "All images pushed to registry"
}

function New-DeploymentScripts {
    Write-Info "Creating deployment scripts..."

    # Create PowerShell image loader script
    $loadScript = @'
# Load exported Docker images on target server
param([string]$ManifestPath = "export-manifest.json")

if (-not (Test-Path $ManifestPath)) {
    Write-Error "export-manifest.json not found"
    exit 1
}

Write-Host "Loading Docker images from exports..."

# Extract version from manifest
$manifest = Get-Content $ManifestPath | ConvertFrom-Json
$version = $manifest.version
Write-Host "Loading images for version: $version"

# Load each image
Get-ChildItem "*.tar*" | ForEach-Object {
    Write-Host "Loading $($_.Name)..."
    
    if ($_.Extension -eq ".zip") {
        # Handle compressed files
        Expand-Archive -Path $_.FullName -DestinationPath "temp" -Force
        $tarFile = Get-ChildItem "temp/*.tar" | Select-Object -First 1
        docker load -i $tarFile.FullName
        Remove-Item "temp" -Recurse -Force
    }
    else {
        docker load -i $_.FullName
    }
    
    Write-Host "Loaded $($_.Name)"
}

Write-Host "All images loaded successfully!"
Write-Host "Run 'docker images' to see loaded images"
'@

    $loadScript | Set-Content (Join-Path $ExportDir "load-images.ps1")

    # Create batch deployment script for cross-platform compatibility
    $deployScript = @'
#!/bin/bash
# Deploy MVS-VR to DigitalOcean using exported images

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

if [[ ! -f "$MANIFEST_FILE" ]]; then
    echo "Error: export-manifest.json not found"
    exit 1
fi

echo "Deploying MVS-VR to DigitalOcean..."

# Load images first
echo "Loading Docker images..."
if [[ -f "$SCRIPT_DIR/load-images.ps1" ]]; then
    powershell -File "$SCRIPT_DIR/load-images.ps1"
else
    "$SCRIPT_DIR/load-images.sh"
fi

# Extract version
VERSION=$(grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4)

echo "Starting services with exported images (version: $VERSION)..."
docker-compose -f docker-compose.yml -f docker-compose.exported.yml up -d

echo "Deployment completed!"
echo "Check service status with: docker-compose ps"
'@

    $deployScript | Set-Content (Join-Path $ExportDir "deploy-to-digitalocean.sh")

    Write-Success "Deployment scripts created"
}

function New-UploadInstructions {
    Write-Info "Creating upload instructions..."

    $instructions = @"
# Docker Image Upload Instructions for DigitalOcean (Windows)

## Overview
This directory contains exported Docker images and deployment scripts for MVS-VR v2.

## Files Included
- ``*.tar`` or ``*.tar.zip`` - Exported Docker images
- ``export-manifest.json`` - Image metadata and information
- ``load-images.ps1`` - PowerShell script to load images on target server
- ``deploy-to-digitalocean.sh`` - Complete deployment script
- ``UPLOAD_INSTRUCTIONS.md`` - This file

## Method 1: Upload via SCP/RSYNC (Recommended)

### Using Windows Subsystem for Linux (WSL)
``````bash
# From WSL terminal
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/
``````

### Using PowerShell with SCP
``````powershell
# Install OpenSSH client if not available
# Upload files
scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/
``````

### Using WinSCP (GUI Option)
1. Download and install WinSCP
2. Connect to your DigitalOcean server
3. Upload the entire docker-exports folder to /opt/mvs-vr/

## Method 2: Using DigitalOcean Container Registry

### Step 1: Push images to registry (from Windows)
``````powershell
# Run the export script with registry push
.\scripts\export-docker-images.ps1 -PushRegistry

# Or manually push after export
doctl registry login
docker push registry.digitalocean.com/mvs-vr/api-gateway:VERSION_TAG
``````

## Verification

After deployment, verify services are running:
``````bash
# Check service status
docker-compose ps

# Check service health
curl http://localhost:3000/health
curl http://localhost:8055/server/health
``````

## Windows-Specific Notes

### PowerShell Execution Policy
If you encounter execution policy errors:
``````powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
``````

### Docker Desktop Requirements
- Ensure Docker Desktop is running
- Enable WSL 2 backend for better performance
- Allocate sufficient resources (4GB+ RAM, 2+ CPUs)

### File Path Considerations
- Use forward slashes in Docker commands
- Be aware of Windows path length limitations
- Consider using shorter export directory paths
"@

    $instructions | Set-Content (Join-Path $ExportDir "UPLOAD_INSTRUCTIONS.md")

    Write-Success "Upload instructions created"
}

function Remove-LocalImages {
    if (-not $Cleanup) {
        Write-Info "Skipping local image cleanup (-Cleanup not specified)"
        return
    }

    Write-Warning "Cleaning up local images..."

    foreach ($service in $Services.Keys) {
        $imageName = "$ProjectName-$service"
        $registryImage = "$Registry/$service"
        
        if ($DryRun) {
            Write-Host "Would remove: ${imageName}:$VersionTag ${imageName}:$LatestTag"
            Write-Host "Would remove: ${registryImage}:$VersionTag ${registryImage}:$LatestTag"
        }
        else {
            docker rmi "${imageName}:$VersionTag" "${imageName}:$LatestTag" 2>$null
            docker rmi "${registryImage}:$VersionTag" "${registryImage}:$LatestTag" 2>$null
        }
    }

    Write-Success "Local images cleaned up"
}

function Show-Summary {
    Write-Success "Docker image export completed!"
    Write-Host ""
    Write-Host "=== Export Summary ===" -ForegroundColor Cyan
    Write-Host "Version: $VersionTag"
    Write-Host "Export Directory: $ExportDir"
    Write-Host "Registry Prefix: $Registry"
    Write-Host ""
    
    if (Test-Path $ExportDir) {
        Write-Host "=== Exported Files ===" -ForegroundColor Cyan
        Get-ChildItem $ExportDir | Format-Table Name, Length, LastWriteTime
        
        Write-Host "=== Total Export Size ===" -ForegroundColor Cyan
        $totalSize = (Get-ChildItem $ExportDir -Recurse | Measure-Object -Property Length -Sum).Sum
        Write-Host "$([math]::Round($totalSize / 1GB, 2)) GB"
        Write-Host ""
    }
    
    Write-Host "=== Next Steps ===" -ForegroundColor Cyan
    Write-Host "1. Review upload instructions: $ExportDir\UPLOAD_INSTRUCTIONS.md"
    Write-Host "2. Upload files to DigitalOcean server"
    Write-Host "3. Run deployment script on server"
    Write-Host "4. Verify service health and functionality"
    Write-Host ""
    Write-Host "=== Quick Upload Command ===" -ForegroundColor Cyan
    Write-Host "scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/"
}

# Main execution
function Main {
    if ($Help) {
        Show-Help
        return
    }

    Write-Info "Starting Docker image export process..."

    Test-Prerequisites
    Build-Images
    Add-RegistryTags
    Export-Images
    Push-ToRegistry
    New-DeploymentScripts
    New-UploadInstructions
    Remove-LocalImages
    Show-Summary

    Write-Success "Export process completed successfully!"
}

# Run main function
Main
