# DigitalOcean Staging Deployment Guide

**Target Environment**: Staging Server  
**Domain**: mvs.kanousai.com  
**Supabase**: Remote staging instance (https://hiyqiqbgiueyyvqoqhht.supabase.co)  
**Date**: January 28, 2025

## 🎯 Overview

This guide provides step-by-step instructions for deploying the MVS-VR v2 application to DigitalOcean staging environment with remote Supabase integration.

## 📋 Prerequisites

### Required Accounts & Access
- DigitalOcean account with billing enabled
- Domain access for mvs.kanousai.com
- Supabase staging project access
- SSH key pair for server access

### Required Information
- **Supabase URL**: `https://hiyqiqbgiueyyvqoqhht.supabase.co`
- **Domain**: `mvs.kanousai.com`
- **SSL Email**: `<EMAIL>`
- **Admin Email**: `<EMAIL>`

## 🚀 Phase 1: DigitalOcean Droplet Setup

### 1.1 Create Droplet

```bash
# Create a new droplet via DigitalOcean CLI (optional)
doctl compute droplet create mvs-staging \
  --region nyc1 \
  --size s-2vcpu-4gb \
  --image ubuntu-22-04-x64 \
  --ssh-keys YOUR_SSH_KEY_ID \
  --enable-monitoring \
  --enable-ipv6 \
  --tag-names mvs,staging
```

**Or via Web Interface:**
- **Name**: `mvs-staging`
- **Region**: New York 1 (nyc1)
- **Size**: Basic - 2 vCPUs, 4GB RAM, 80GB SSD
- **Image**: Ubuntu 22.04 LTS
- **Authentication**: SSH Key
- **Monitoring**: Enabled
- **IPv6**: Enabled

### 1.2 Initial Server Setup

```bash
# Connect to your droplet
ssh root@YOUR_DROPLET_IP

# Update system
apt update && apt upgrade -y

# Install required packages
apt install -y curl wget git nginx certbot python3-certbot-nginx ufw

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create application user
useradd -m -s /bin/bash mvs
usermod -aG docker mvs
```

### 1.3 Configure Firewall

```bash
# Configure UFW
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable
```

## 🌐 Phase 2: Domain & SSL Setup

### 2.1 DNS Configuration

Configure DNS records for `mvs.kanousai.com`:

```
Type    Name    Value               TTL
A       @       YOUR_DROPLET_IP     300
A       www     YOUR_DROPLET_IP     300
A       admin   YOUR_DROPLET_IP     300
A       api     YOUR_DROPLET_IP     300
```

### 2.2 SSL Certificate Setup

```bash
# Install SSL certificate
certbot --nginx -d mvs.kanousai.com -d www.mvs.kanousai.com -d admin.mvs.kanousai.com -d api.mvs.kanousai.com --email <EMAIL> --agree-tos --non-interactive

# Test auto-renewal
certbot renew --dry-run
```

## 📦 Phase 3: Application Deployment

### 3.1 Clone Repository

```bash
# Switch to mvs user
su - mvs

# Clone repository
git clone https://github.com/your-org/mvs-vr-v2.git
cd mvs-vr-v2

# Checkout staging branch (if exists)
git checkout staging || git checkout main
```

### 3.2 Environment Configuration

```bash
# Copy staging environment file
cp .env.staging .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```bash
# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=your-staging-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-staging-service-role-key

# Security
JWT_SECRET=your-jwt-secret-here
CSRF_SECRET=your-csrf-secret-here
API_KEY=your-api-key-here

# Database (for Directus)
POSTGRES_PASSWORD=9elskdUeo@I!
DIRECTUS_DB_PASSWORD=9elskdUeo@I!
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=9elskdUeo@I!

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=9elskdUeo@I!

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### 3.3 Build and Deploy

```bash
# Build Docker images
docker-compose -f docker-compose.staging.yml build

# Start services
docker-compose -f docker-compose.staging.yml up -d

# Check service status
docker-compose -f docker-compose.staging.yml ps
```

## 🔧 Phase 4: Service Configuration

### 4.1 Nginx Configuration

```bash
# Copy staging nginx config
sudo cp deployment/nginx/staging.conf /etc/nginx/sites-available/mvs-staging
sudo ln -s /etc/nginx/sites-available/mvs-staging /etc/nginx/sites-enabled/

# Test and reload nginx
sudo nginx -t
sudo systemctl reload nginx
```

### 4.2 Verify Services

```bash
# Check all containers are running
docker ps

# Check logs
docker-compose -f docker-compose.staging.yml logs api
docker-compose -f docker-compose.staging.yml logs directus
docker-compose -f docker-compose.staging.yml logs redis

# Test endpoints
curl -f http://localhost:3000/health
curl -f http://localhost:8055/server/health
```

## 🧪 Phase 5: Testing & Validation

### 5.1 Health Checks

```bash
# API Health Check
curl https://mvs.kanousai.com/api/health

# Directus Health Check
curl https://admin.mvs.kanousai.com/server/health

# Supabase Connection Test
curl -H "apikey: YOUR_ANON_KEY" https://hiyqiqbgiueyyvqoqhht.supabase.co/rest/v1/
```

### 5.2 Functional Testing

1. **Admin Portal**: https://admin.mvs.kanousai.com
   - Login with: <EMAIL> / 9elskdUeo@I!
   - Verify dashboard loads
   - Test vendor creation

2. **API Endpoints**: https://mvs.kanousai.com/api
   - Test authentication endpoints
   - Verify Supabase integration
   - Check asset upload functionality

3. **Monitoring**: https://mvs.kanousai.com:3001
   - Login to Grafana
   - Verify metrics collection
   - Check service dashboards

## 📊 Phase 6: Monitoring Setup

### 6.1 Access Monitoring Tools

- **Grafana**: https://mvs.kanousai.com:3001 (admin/9elskdUeo@I!)
- **Prometheus**: https://mvs.kanousai.com:9090
- **Logs**: `docker-compose logs -f`

### 6.2 Set Up Alerts

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/docker-containers

# Set up monitoring alerts (optional)
# Configure email notifications in Grafana
```

## 🔄 Phase 7: Maintenance & Updates

### 7.1 Update Deployment

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose -f docker-compose.staging.yml down
docker-compose -f docker-compose.staging.yml build --no-cache
docker-compose -f docker-compose.staging.yml up -d
```

### 7.2 Backup Strategy

```bash
# Backup Directus data
docker exec mvs-postgres-staging pg_dump -U postgres mvs_staging > backup_$(date +%Y%m%d).sql

# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

## 🚨 Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   ```bash
   sudo certbot renew --force-renewal
   sudo systemctl reload nginx
   ```

2. **Docker Permission Issues**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

3. **Service Connection Issues**
   ```bash
   # Check network connectivity
   docker network ls
   docker network inspect mvs-vr-v2_mvs-network
   ```

4. **Supabase Connection Issues**
   ```bash
   # Test from container
   docker exec mvs-api-staging curl -H "apikey: $SUPABASE_ANON_KEY" $SUPABASE_URL/rest/v1/
   ```

## ✅ Deployment Checklist

- [ ] DigitalOcean droplet created and configured
- [ ] DNS records configured for mvs.kanousai.com
- [ ] SSL certificates installed and working
- [ ] Environment variables configured
- [ ] Docker services built and running
- [ ] Nginx reverse proxy configured
- [ ] Supabase connection verified
- [ ] Admin portal accessible
- [ ] API endpoints responding
- [ ] Monitoring tools accessible
- [ ] Backup strategy implemented

## 📞 Support

For deployment issues:
- Check logs: `docker-compose -f docker-compose.staging.yml logs`
- Review health checks: `curl https://mvs.kanousai.com/health`
- Contact: <EMAIL>

---

**Deployment Status**: Ready for staging deployment  
**Last Updated**: January 28, 2025
