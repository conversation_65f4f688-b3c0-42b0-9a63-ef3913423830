#!/bin/bash

# MVS-VR Docker Export System Validation Script
# This script validates that all components of the export system are properly configured

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Validation results
VALIDATION_PASSED=true
ISSUES_FOUND=()

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

add_issue() {
    ISSUES_FOUND+=("$1")
    VALIDATION_PASSED=false
}

check_file_exists() {
    local file_path="$1"
    local description="$2"
    
    if [[ -f "$file_path" ]]; then
        log_success "$description exists"
        return 0
    else
        log_error "$description missing: $file_path"
        add_issue "$description missing"
        return 1
    fi
}

check_file_executable() {
    local file_path="$1"
    local description="$2"
    
    if [[ -x "$file_path" ]]; then
        log_success "$description is executable"
        return 0
    else
        log_warning "$description is not executable: $file_path"
        return 1
    fi
}

check_docker_files() {
    log_info "Checking Docker configuration files..."
    
    local docker_files=(
        "$PROJECT_DIR/docker-compose.yml:Main Docker Compose file"
        "$PROJECT_DIR/docker-compose.registry.yml:Registry Docker Compose file"
        "$PROJECT_DIR/docker-compose.staging.yml:Staging Docker Compose file"
        "$PROJECT_DIR/docker-compose.prod.yml:Production Docker Compose file"
    )
    
    for file_desc in "${docker_files[@]}"; do
        IFS=':' read -r file_path description <<< "$file_desc"
        check_file_exists "$file_path" "$description"
    done
    
    # Check Dockerfiles
    local dockerfile_dir="$PROJECT_DIR/implementation/server"
    local dockerfiles=(
        "Dockerfile.gateway"
        "Dockerfile.asset-service"
        "Dockerfile.scene-service"
        "Dockerfile.blueprint-service"
        "Dockerfile.llm-service"
        "Dockerfile.auth-service"
        "Dockerfile.analytics-service"
        "Dockerfile.monitoring-service"
        "Dockerfile.directus"
    )
    
    for dockerfile in "${dockerfiles[@]}"; do
        check_file_exists "$dockerfile_dir/$dockerfile" "Dockerfile: $dockerfile"
    done
}

check_export_scripts() {
    log_info "Checking export and deployment scripts..."
    
    local scripts=(
        "$SCRIPT_DIR/export-docker-images.sh:Docker Export Script (Bash)"
        "$SCRIPT_DIR/export-docker-images.ps1:Docker Export Script (PowerShell)"
        "$SCRIPT_DIR/deploy-with-images.sh:Automated Deployment Script"
        "$SCRIPT_DIR/deploy-staging.sh:Staging Deployment Script"
    )
    
    for script_desc in "${scripts[@]}"; do
        IFS=':' read -r script_path description <<< "$script_desc"
        if check_file_exists "$script_path" "$description"; then
            # Check if bash scripts are executable (skip on Windows)
            if [[ "$script_path" == *.sh ]] && [[ "$(uname -s)" != "MINGW"* ]] && [[ "$(uname -s)" != "CYGWIN"* ]]; then
                check_file_executable "$script_path" "$description"
            fi
        fi
    done
}

check_documentation() {
    log_info "Checking documentation files..."
    
    local docs=(
        "$PROJECT_DIR/docs/deployment-guides/docker-image-export-guide.md:Docker Export Guide"
        "$PROJECT_DIR/docs/deployment-guides/DOCKER_EXPORT_README.md:Docker Export README"
        "$PROJECT_DIR/docs/deployment-guides/digitalocean-step-by-step.md:DigitalOcean Guide"
    )
    
    for doc_desc in "${docs[@]}"; do
        IFS=':' read -r doc_path description <<< "$doc_desc"
        check_file_exists "$doc_path" "$description"
    done
}

check_prerequisites() {
    log_info "Checking system prerequisites..."
    
    local required_tools=("docker" "docker-compose")
    local optional_tools=("doctl" "ssh" "rsync")
    
    # Check required tools
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "$tool is installed"
        else
            log_error "$tool is not installed (required)"
            add_issue "$tool not installed"
        fi
    done
    
    # Check optional tools
    for tool in "${optional_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "$tool is installed"
        else
            log_warning "$tool is not installed (optional but recommended)"
        fi
    done
    
    # Check Docker daemon
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            log_success "Docker daemon is running"
        else
            log_error "Docker daemon is not running"
            add_issue "Docker daemon not running"
        fi
    fi
}

check_project_structure() {
    log_info "Checking project structure..."
    
    local required_dirs=(
        "$PROJECT_DIR/implementation/server:Server implementation directory"
        "$PROJECT_DIR/scripts:Scripts directory"
        "$PROJECT_DIR/docs/deployment-guides:Deployment guides directory"
    )
    
    for dir_desc in "${required_dirs[@]}"; do
        IFS=':' read -r dir_path description <<< "$dir_desc"
        if [[ -d "$dir_path" ]]; then
            log_success "$description exists"
        else
            log_error "$description missing: $dir_path"
            add_issue "$description missing"
        fi
    done
}

validate_docker_compose_syntax() {
    log_info "Validating Docker Compose syntax..."
    
    local compose_files=(
        "$PROJECT_DIR/docker-compose.yml"
        "$PROJECT_DIR/docker-compose.registry.yml"
        "$PROJECT_DIR/docker-compose.staging.yml"
    )
    
    for compose_file in "${compose_files[@]}"; do
        if [[ -f "$compose_file" ]]; then
            if docker-compose -f "$compose_file" config &> /dev/null; then
                log_success "$(basename "$compose_file") syntax is valid"
            else
                log_error "$(basename "$compose_file") has syntax errors"
                add_issue "$(basename "$compose_file") syntax errors"
            fi
        fi
    done
}

test_export_script_help() {
    log_info "Testing export script help functionality..."
    
    local export_script="$SCRIPT_DIR/export-docker-images.sh"
    if [[ -f "$export_script" ]]; then
        if bash "$export_script" --help &> /dev/null; then
            log_success "Export script help works"
        else
            log_error "Export script help failed"
            add_issue "Export script help failed"
        fi
    fi
}

check_environment_files() {
    log_info "Checking environment configuration..."
    
    local env_files=(
        "$PROJECT_DIR/.env.example:Environment example file"
        "$PROJECT_DIR/.env.staging.example:Staging environment example"
    )
    
    for env_desc in "${env_files[@]}"; do
        IFS=':' read -r env_path description <<< "$env_desc"
        if [[ -f "$env_path" ]]; then
            log_success "$description exists"
        else
            log_warning "$description missing (recommended): $env_path"
        fi
    done
}

show_validation_summary() {
    echo ""
    echo "=== Validation Summary ==="
    
    if [[ "$VALIDATION_PASSED" == "true" ]]; then
        log_success "All critical validations passed!"
        echo ""
        echo "✅ Docker export system is properly configured"
        echo "✅ All required files are present"
        echo "✅ Scripts are ready for use"
        echo ""
        echo "Next steps:"
        echo "1. Test the export script: ./scripts/export-docker-images.sh --dry-run"
        echo "2. Review the documentation: docs/deployment-guides/DOCKER_EXPORT_README.md"
        echo "3. Configure your DigitalOcean server for deployment"
    else
        log_error "Validation failed with ${#ISSUES_FOUND[@]} issue(s):"
        echo ""
        for issue in "${ISSUES_FOUND[@]}"; do
            echo "❌ $issue"
        done
        echo ""
        echo "Please resolve these issues before using the export system."
        return 1
    fi
}

show_usage_examples() {
    echo ""
    echo "=== Usage Examples ==="
    echo ""
    echo "1. Basic image export:"
    echo "   ./scripts/export-docker-images.sh --compress"
    echo ""
    echo "2. Export with custom version:"
    echo "   ./scripts/export-docker-images.sh --version v1.0.0 --compress"
    echo ""
    echo "3. Export and push to registry:"
    echo "   ./scripts/export-docker-images.sh --push-registry"
    echo ""
    echo "4. Automated deployment:"
    echo "   ./scripts/deploy-with-images.sh --server-ip YOUR_SERVER_IP"
    echo ""
    echo "5. Dry run to preview actions:"
    echo "   ./scripts/export-docker-images.sh --dry-run"
    echo ""
    echo "For more examples, see: docs/deployment-guides/DOCKER_EXPORT_README.md"
}

# Main validation process
main() {
    log_info "Starting Docker export system validation..."
    echo ""
    
    check_prerequisites
    echo ""
    
    check_project_structure
    echo ""
    
    check_docker_files
    echo ""
    
    check_export_scripts
    echo ""
    
    check_documentation
    echo ""
    
    check_environment_files
    echo ""
    
    validate_docker_compose_syntax
    echo ""
    
    test_export_script_help
    echo ""
    
    show_validation_summary
    
    if [[ "$VALIDATION_PASSED" == "true" ]]; then
        show_usage_examples
    fi
}

# Run validation
main "$@"
